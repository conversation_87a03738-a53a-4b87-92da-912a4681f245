task for return request type 'exchange'

---

### 1. Implement Yamato Barcode Scan to Find Return Requests

* **Locate/Create Input Field:**
    * Identify or create the UI input element designated for scanning/entering the Yamato barcode (tracking number).
* **Implement Barcode Scanning Logic:**
    * Integrate with a barcode scanning library or device API to enable scanning directly into this input field.
    * Allow manual entry of the Yamato tracking number as a fallback.
* **Develop API Call Function:**
    * Create a function to make an API request to your backend.
    * This request should take the Yamato tracking number as a parameter.
    * The backend should be responsible for searching for return requests associated with this tracking number.
* **Handle API Response:**
    * On receiving the response, parse the data to identify the matching return request(s).
    * If a request is found, store its details (especially the `processed` status and item list) in the application's state.
    * If no request is found, display an appropriate message to the user (e.g., "No return request found for this tracking number.").
* **Trigger Display Logic:**
    * Based on the fetched return request's `processed` status, trigger the display of relevant information as outlined in subsequent tasks.

---

### 2. Handle Return Requests with 'pending' Status

* **Conditional UI Rendering:**
    * If the `processed` status of the found return request is **'pending'**:
        * Display a list of return items that need to be processed for this request. Each item should clearly show details like name, SKU, quantity, and a visual indicator for its scan status (e.g., "Not Scanned," "Scanned ✔️").

    * **2a. Implement SKU Scanning for Outgoing Stock Verification:**
        * **Add New Barcode Input:** Create a new, distinct input field or button to trigger SKU barcode scanning for item verification.
        * **SKU Scanning Logic:**
            * When a SKU barcode is scanned (or manually entered):
                * Iterate through the displayed list of return items for the 'pending' request.
                * Compare the scanned SKU with the SKU of each line item.
                * If a match is found for an item not yet marked as scanned:
                    * Automatically mark that specific line item as "Scanned" in the UI.
                    * Update the application's state to reflect this change.
                    * Consider visually highlighting the matched and now "Scanned" item.
                * If the scanned SKU does not match any of the *unscanned* required items, trigger the error sound (see 2c).
                * If all items are already marked as scanned, provide a message like "All items already scanned."

    * **2b. Implement "Mark Package Scanned & Set Status" Button:**
        * **Add Button:** Create a button labeled something like "Confirm Items & Mark as Exchange Shipped".
        * **Button Logic:**
            * **Pre-condition Check:** Enable this button only if all return items in the list have been marked as "Scanned".
            * **API Call:** On click, make an API request to update the return request's status to **'exchange_shipped'**. Pass the return request ID.
            * **UI Update:** Upon successful API response, refresh the return request details or navigate/update the UI to reflect the new status (this might trigger the logic in task 3).
            * If the API call fails, display an error message and play the error sound (see 2c).

    * **2c. Implement Error Sound:**
        * **Choose Sound:** Select or prepare a short, distinct error sound file.
        * **Sound Function:** Create a JavaScript function to play the error sound.
        * **Trigger Conditions:**
            * Call this function if a scanned SKU in step 2a does not match any required unscanned items.
            * Call this function if the API call in step 2b fails.

---

### 3. Handle Return Requests with 'exchange_shipped' Status

* **Conditional UI Rendering:**
    * If the `processed` status of the found return request is **'exchange_shipped'**:
        * Display a list of return items expected from the customer. Similar to the 'pending' state, show item details and a scan status indicator.

    * **3a. Implement SKU Scanning for Incoming Customer Stock Verification:**
        * **Reuse/Add Barcode Input:** Utilize a similar (or the same if contextually clear) input field/button for scanning SKU barcodes of items returned by the customer.
        * **SKU Scanning Logic:**
            * When a SKU barcode is scanned:
                * Iterate through the displayed list of expected return items for the 'exchange_shipped' request.
                * Compare the scanned SKU with the SKU of each line item.
                * If a match is found for an item not yet marked as "Scanned" (as received):
                    * Automatically mark that line item as "Scanned" in the UI.
                    * Update the application's state.
                * If the scanned SKU does not match any of the *unscanned* expected items, trigger the error sound (see 3c).
                * If all expected items are already marked as scanned, provide a message like "All expected items already scanned."

    * **3b. Implement "Mark Package Received & Set Status" Button:**
        * **Add Button:** Create a button labeled something like "Confirm Received Items & Mark as Returned Received".
        * **Button Logic:**
            * **Pre-condition Check:** Enable this button only if all expected return items in the list have been marked as "Scanned".
            * **API Call:** On click, make an API request to update the return request's status to **'returned_received'**. Pass the return request ID.
            * **UI Update:** Upon successful API response, refresh the return request details or update the UI to reflect the new status (this might trigger the logic in task 4).
            * If the API call fails, display an error message and play the error sound (see 3c).

    * **3c. Implement Error Sound (Reuse or Separate):**
        * Reuse the error sound function from 2c.
        * **Trigger Conditions:**
            * Call this function if a scanned SKU in step 3a does not match any expected unscanned items.
            * Call this function if the API call in step 3b fails.

---

### 4. Handle Return Requests with 'returned_received' Status

* **Conditional UI Rendering:**
    * If the `processed` status of the found return request is **'returned_received'**:
        * Display the list of received items (optionally with their "Scanned" status from the previous step for confirmation).
        * Provide a clear UI section for an administrator to confirm and complete the return.
* **Implement "Confirm & Complete Return" Action:**
    * **Add Button/Action:** Create a button labeled "Confirm & Complete Return" or similar.
    * **Confirmation Modal (Recommended):** Before proceeding, show a confirmation dialog (e.g., "Are you sure you want to complete this return request? This action cannot be undone.").
    * **API Call:** On confirmation, make an API request to set the return request status to its final "completed" (or equivalent) state.
    * **UI Update:**
        * Upon successful API response, display a success message (e.g., "Return request completed successfully!").
        * Potentially clear the current return request from the UI or redirect to a main dashboard/search page.
    * **Error Handling:** If the API call fails, display an appropriate error message.