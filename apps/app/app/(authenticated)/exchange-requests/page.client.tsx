'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import Link from 'next/link';
import {
  generateYamatoCsv,
  type getExchangeRequests,
} from '../return-requests/actions';
import { columns } from '../return-requests/components/return-request-column';
import { ReturnRequestTable } from '../return-requests/components/return-request-table';
import { UploadYamatoCsv } from '../return-requests/components/upload-yamato-csv';

export const ReturnRequestsPageClient = ({
  exchangeRequests,
}: {
  exchangeRequests: Awaited<ReturnType<typeof getExchangeRequests>>;
}) => {
  return (
    <Tabs defaultValue="exchange">
      <TabsList>
        <TabsTrigger value="exchange">Exchange Requests</TabsTrigger>
        <TabsTrigger value="yamato">Yamato Processing</TabsTrigger>
      </TabsList>
      <TabsContent value="exchange">
        <ReturnRequestTable columns={columns} initialData={exchangeRequests} />
      </TabsContent>
      <TabsContent value="yamato">
        <Card className="mt-4 flex flex-col items-start gap-2">
          <CardContent className="space-y-4 ">
            <div>
              <p className="mb-1 text-sm">
                1. Download the CSV file and upload it to Yamato.
              </p>
              <div>
                <Button
                  onClick={async () => {
                    const result = await generateYamatoCsv();

                    if (result.error) {
                      console.error('Error generating CSV:', result.error);
                      return;
                    }

                    if (result.success && result.data) {
                      // Create blob and download
                      const binaryString = atob(result.data);
                      const bytes = new Uint8Array(binaryString.length);
                      for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                      }

                      const blob = new Blob([bytes], { type: result.mimeType });
                      const url = URL.createObjectURL(blob);

                      const a = document.createElement('a');
                      a.href = url;
                      a.download = result.filename;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    }
                  }}
                >
                  Download Yamato CSV
                </Button>
              </div>
            </div>
            <div>
              <p className="mb-1 text-sm">
                2. Go to Yamato and upload the CSV file.
              </p>
              <Link href="https://bmypage.kuronekoyamato.co.jp/bmypage/">
                <Button>Go to Yamato</Button>
              </Link>
            </div>
            <div>
              <p className="mb-1 text-sm">
                3. Upload the CSV file from Yamato.
              </p>
              <UploadYamatoCsv />
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};
