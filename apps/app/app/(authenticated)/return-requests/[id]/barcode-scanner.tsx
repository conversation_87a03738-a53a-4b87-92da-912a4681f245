'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';

import { useToast } from '@repo/design-system/components/ui/use-toast';
import { CheckCircle, Package, Volume2, VolumeX, XCircle } from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number; // Track how many have been scanned
}

interface BarcodeScannerProps {
  returnItems: ReturnItem[];
  exchangeType: string | null;
  returnNumber: string;
  onItemScanned: (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => void;
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
}

export function BarcodeScanner({
  returnItems,
  exchangeType,
  returnNumber,
  onItemScanned,
  onSubmit,
}: BarcodeScannerProps) {
  const { toast } = useToast();
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
    itemId?: string;
  } | null>(null);
  const [productChecklist, setProductChecklist] = useState<ReturnItem[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [delayDebounce, setDelayDebounce] = useState<NodeJS.Timeout>();
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [barcode, setBarcode] = useState('');
  const barcodeInputRef = useRef<HTMLInputElement>(null);

  // Two-step scanning state for returns
  const [returnPackageVerified, setReturnPackageVerified] = useState(false);
  const [scanningStep, setScanningStep] = useState<'package' | 'items'>(
    'package'
  );

  // Audio feedback
  const errorAudio = useRef<HTMLAudioElement>(null);
  const successAudio = useRef<HTMLAudioElement>(null);

  // Initialize product checklist from return items
  useEffect(() => {
    const initialChecklist = returnItems.map((item) => ({
      ...item,
      currentQty: 0,
    }));
    setProductChecklist(initialChecklist);
  }, [returnItems]);

  // Initialize audio elements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      errorAudio.current = new Audio('/audio/error.mp3');
      successAudio.current = new Audio('/audio/success.mp3');
    }
  }, []);

  // Auto-focus the barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  }, []);

  // Keep focus on barcode input after scanning
  useEffect(() => {
    if (barcodeInputRef.current && !barcode) {
      barcodeInputRef.current.focus();
    }
  }, [barcode]);

  const playAudio = useCallback(
    (isSuccess: boolean) => {
      if (!audioEnabled) {
        return;
      }

      try {
        if (isSuccess && successAudio.current) {
          successAudio.current.play();
        } else if (!isSuccess && errorAudio.current) {
          errorAudio.current.play();
        }
      } catch {
        // Audio playback failed - this is not critical
      }
    },
    [audioEnabled]
  );

  const handleReturnPackageVerification = useCallback(
    (barcode: string) => {
      setErrorMessage('');

      // Check if scanned barcode matches the return number
      if (barcode.toLowerCase() === returnNumber.toLowerCase()) {
        setReturnPackageVerified(true);
        setScanningStep('items');
        playAudio(true);
        setScanResult({
          success: true,
          message: 'Return package verified! Now scan individual items.',
        });
      } else {
        playAudio(false);
        setErrorMessage('Scanned barcode does not match the return number');
        setScanResult({
          success: false,
          message: 'Scanned barcode does not match the return number',
        });
      }
    },
    [returnNumber, playAudio]
  );

  const handleCheckProduct = useCallback(
    (barcode: string) => {
      setErrorMessage('');
      const index = productChecklist.findIndex(
        (item) =>
          item.barcode && item.barcode.toLowerCase() === barcode.toLowerCase()
      );

      if (index !== -1) {
        const newList = [...productChecklist];
        const currentQty = newList[index].currentQty || 0;
        if (currentQty + 1 <= newList[index].quantity) {
          newList[index].currentQty = currentQty + 1;
          setProductChecklist(newList);
          onItemScanned(newList[index].id, barcode, newList[index].currentQty);
          playAudio(true);
          setScanResult({
            success: true,
            message: `Successfully scanned: ${newList[index].title}`,
            itemId: newList[index].id,
          });
          return;
        }
        setErrorMessage('Maximum amount reached');
        playAudio(false);
        setScanResult({
          success: false,
          message: 'Maximum amount reached',
        });
        return;
      }
      playAudio(false);
      setErrorMessage('Scanned barcode does not match any product in the list');
      setScanResult({
        success: false,
        message: 'Scanned barcode does not match any product in the list',
      });
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleMarkAsScanned = useCallback(
    (itemBarcode: string) => {
      const index = productChecklist.findIndex(
        (item) => item.barcode === itemBarcode
      );
      if (index !== -1) {
        const newList = [...productChecklist];
        newList[index].currentQty = newList[index].quantity;
        setProductChecklist(newList);
        onItemScanned(newList[index].id, itemBarcode, newList[index].quantity);
      }
    },
    [productChecklist, onItemScanned]
  );

  const handleInput = useCallback(
    (value: string) => {
      clearTimeout(delayDebounce);
      if (value.trim()) {
        const delayDebounceTimeout = setTimeout(() => {
          // Route to appropriate handler based on exchange type and scanning step
          if (exchangeType === 'return' && scanningStep === 'package') {
            handleReturnPackageVerification(value.trim());
          } else if (exchangeType === 'return' && scanningStep === 'items') {
            handleCheckProduct(value.trim());
          } else if (exchangeType === 'exchange') {
            // For exchanges, we don't process scanning yet
            setErrorMessage('Exchange scanning is coming soon');
            setScanResult({
              success: false,
              message: 'Exchange scanning is coming soon',
            });
          } else {
            // Default behavior for null exchange type (legacy returns)
            handleCheckProduct(value.trim());
          }
          setBarcode('');
          // Refocus the input after processing
          if (barcodeInputRef.current) {
            barcodeInputRef.current.focus();
          }
        }, 300); // Reduced delay for faster scanning
        setDelayDebounce(delayDebounceTimeout);
      }
    },
    [
      delayDebounce,
      exchangeType,
      scanningStep,
      handleReturnPackageVerification,
      handleCheckProduct,
    ]
  );

  const handleBarcodeSubmit = () => {
    if (!barcode.trim()) {
      return;
    }
    clearTimeout(delayDebounce);

    // Route to appropriate handler based on exchange type and scanning step
    if (exchangeType === 'return' && scanningStep === 'package') {
      handleReturnPackageVerification(barcode.trim());
    } else if (exchangeType === 'return' && scanningStep === 'items') {
      handleCheckProduct(barcode.trim());
    } else if (exchangeType === 'exchange') {
      // For exchanges, we don't process scanning yet
      setErrorMessage('Exchange scanning is coming soon');
      setScanResult({
        success: false,
        message: 'Exchange scanning is coming soon',
      });
    } else {
      // Default behavior for null exchange type (legacy returns)
      handleCheckProduct(barcode.trim());
    }

    setBarcode('');
    // Refocus the input after manual submission
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleBarcodeSubmit();
    }
  };

  // Calculate progress based on product checklist
  const scannedCount = productChecklist.filter(
    (item) => item.currentQty === item.quantity
  ).length;
  const totalCount = productChecklist.length;
  const allScanned = scannedCount === totalCount && totalCount > 0;

  // Helper functions for UI content
  const getCardTitle = () => {
    if (exchangeType === 'exchange') {
      return 'Exchange Processing';
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return 'Return Package Verification';
    }
    return 'Item Verification';
  };

  const getCardDescription = () => {
    if (exchangeType === 'exchange') {
      return 'Exchange scanning functionality is coming soon';
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return `Scan the return package label (${returnNumber}) to verify the return`;
    }
    return `Scan barcodes to verify return items (${scannedCount}/${totalCount} verified)`;
  };

  const getProgressWidth = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return returnPackageVerified ? '50%' : '0%';
    }
    return `${(scannedCount / totalCount) * 100}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {getCardTitle()}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="ml-auto"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
          </Button>
        </CardTitle>
        <CardDescription>{getCardDescription()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Show coming soon message for exchanges */}
        {exchangeType === 'exchange' && (
          <div className="flex items-center justify-center rounded-lg border border-blue-200 bg-blue-50 p-8 text-blue-800">
            <div className="text-center">
              <Package className="mx-auto mb-4 h-12 w-12" />
              <h3 className="mb-2 font-semibold text-lg">
                Exchange Scanning Coming Soon
              </h3>
              <p className="text-sm">
                Exchange barcode scanning functionality will be available in a
                future update.
              </p>
            </div>
          </div>
        )}

        {/* Progress indicator - only show for returns */}
        {exchangeType !== 'exchange' && (
          <div className="h-2 w-full rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-blue-600 transition-all duration-300"
              style={{ width: getProgressWidth() }}
            />
          </div>
        )}

        {/* Error message */}
        {errorMessage && (
          <div className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
            <XCircle className="h-4 w-4" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}

        {/* Barcode Scanner Input - only show for non-exchange types */}
        {exchangeType !== 'exchange' && (
          <div className="space-y-2">
            <Label htmlFor="barcode-input" className="font-medium text-sm">
              {exchangeType === 'return' && scanningStep === 'package'
                ? 'Return Package Scanner'
                : 'Barcode Scanner Input'}
            </Label>
            <div className="flex gap-2">
              <Input
                ref={barcodeInputRef}
                id="barcode-input"
                type="text"
                value={barcode}
                onChange={(e) => {
                  setBarcode(e.target.value);
                  handleInput(e.target.value);
                }}
                placeholder={
                  exchangeType === 'return' && scanningStep === 'package'
                    ? `Scan return package label (${returnNumber})...`
                    : 'Focus here and scan barcode...'
                }
                className="flex-1 rounded-md border border-gray-300 px-3 py-2 font-mono"
                onKeyDown={handleKeyDown}
                autoComplete="off"
                autoFocus
              />
              <Button
                onClick={handleBarcodeSubmit}
                disabled={!barcode.trim()}
                variant="outline"
              >
                Submit
              </Button>
            </div>
            <p className="text-gray-500 text-sm">
              {exchangeType === 'return' && scanningStep === 'package'
                ? 'Scan the return package label to verify the return before scanning individual items.'
                : 'Keep this input focused and use your external barcode scanner to scan items.'}
            </p>
          </div>
        )}

        {/* Scan result */}
        {scanResult && (
          <div
            className={`flex items-center gap-2 rounded-lg p-3 ${
              scanResult.success
                ? 'border border-green-200 bg-green-50 text-green-800'
                : 'border border-red-200 bg-red-50 text-red-800'
            }`}
          >
            {scanResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <span className="text-sm">{scanResult.message}</span>
          </div>
        )}

        {/* Items list - only show for returns and when scanning items */}
        {exchangeType !== 'exchange' &&
          (exchangeType !== 'return' || scanningStep === 'items') && (
            <div className="space-y-2">
              <h4 className="font-medium">Return Items</h4>
              {productChecklist.map((product) => {
                const scanSuccess = product.currentQty === product.quantity;

                return (
                  <div
                    key={product.id}
                    className={`rounded-lg border p-3 ${
                      scanSuccess
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium">{product.title}</p>
                        {product.sku && (
                          <p className="text-gray-600 text-sm">
                            SKU: {product.sku}
                          </p>
                        )}
                        {product.barcode && (
                          <p className="text-gray-600 text-sm">
                            Barcode: {product.barcode}
                          </p>
                        )}
                        <p className="text-gray-600 text-sm">
                          Qty: {product.quantity}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <span
                            className={`text-sm ${
                              scanSuccess ? 'text-green-600' : 'text-red-600'
                            }`}
                          >
                            {product.currentQty} / {product.quantity} scanned
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant={scanSuccess ? 'default' : 'secondary'}>
                          {scanSuccess ? 'Complete' : 'Pending'}
                        </Badge>
                        {scanSuccess ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">Success</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1 text-red-600">
                              <XCircle className="h-4 w-4" />
                              <span className="text-sm">Scan to Match</span>
                            </div>
                            {product.barcode && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleMarkAsScanned(product.barcode as string)
                                }
                                disabled={scanSuccess}
                              >
                                Mark as scanned
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

        {/* Submit button - only show when appropriate */}
        {exchangeType !== 'exchange' &&
          (exchangeType !== 'return' ||
            (returnPackageVerified && allScanned)) &&
          onSubmit && (
            <div className="flex justify-center pt-4">
              <Button
                onClick={() => {
                  const scannedItems = productChecklist.map((item) => ({
                    itemId: item.id,
                    scannedQuantity: item.currentQty || 0,
                  }));
                  onSubmit(scannedItems);
                  toast({
                    title: 'Success',
                    description: 'All items have been verified successfully!',
                  });
                }}
                className="w-full"
              >
                Complete Verification
              </Button>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
