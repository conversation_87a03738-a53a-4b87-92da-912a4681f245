'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';

import { useToast } from '@repo/design-system/components/ui/use-toast';
import { CheckCircle, Package, Volume2, VolumeX, XCircle } from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number; // Track how many have been scanned
}

interface BarcodeScannerProps {
  returnItems: ReturnItem[];
  exchangeType: string | null;
  returnNumber: string;
  processed?: string; // Add processed status to determine exchange step
  onItemScanned: (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => void;
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
}

export function BarcodeScanner({
  returnItems,
  exchangeType,
  returnNumber,
  processed,
  onItemScanned,
  onSubmit,
}: BarcodeScannerProps) {
  const { toast } = useToast();
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
    itemId?: string;
  } | null>(null);
  const [productChecklist, setProductChecklist] = useState<ReturnItem[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [delayDebounce, setDelayDebounce] = useState<NodeJS.Timeout>();
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [barcode, setBarcode] = useState('');
  const barcodeInputRef = useRef<HTMLInputElement>(null);

  // Two-step scanning state for returns
  const [returnPackageVerified, setReturnPackageVerified] = useState(false);
  const [scanningStep, setScanningStep] = useState<'package' | 'items'>(
    'package'
  );

  // State for exchange workflow - set initial step based on processed status
  const getInitialExchangeStep = () => {
    if (exchangeType === 'exchange' && processed) {
      if (processed === 'pending') {
        return 'outgoing';
      }
      if (processed === 'exchange_shipped') {
        return 'incoming';
      }
    }
    return 'outgoing';
  };

  const [exchangeStep, setExchangeStep] = useState<'outgoing' | 'incoming'>(
    getInitialExchangeStep()
  );

  // Audio feedback
  const errorAudio = useRef<HTMLAudioElement>(null);
  const successAudio = useRef<HTMLAudioElement>(null);

  // Initialize product checklist from return items
  useEffect(() => {
    const initialChecklist = returnItems.map((item) => ({
      ...item,
      currentQty: 0,
    }));
    setProductChecklist(initialChecklist);
  }, [returnItems]);

  // Initialize audio elements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      errorAudio.current = new Audio('/audio/error.mp3');
      successAudio.current = new Audio('/audio/success.mp3');
    }
  }, []);

  // Auto-focus the barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  }, []);

  // Keep focus on barcode input after scanning
  useEffect(() => {
    if (barcodeInputRef.current && !barcode) {
      barcodeInputRef.current.focus();
    }
  }, [barcode]);

  const playAudio = useCallback(
    (isSuccess: boolean) => {
      if (!audioEnabled) {
        return;
      }

      try {
        if (isSuccess && successAudio.current) {
          successAudio.current.play();
        } else if (!isSuccess && errorAudio.current) {
          errorAudio.current.play();
        }
      } catch {
        // Audio playback failed - this is not critical
      }
    },
    [audioEnabled]
  );

  const handleReturnPackageVerification = useCallback(
    (barcode: string) => {
      setErrorMessage('');

      // Check if scanned barcode matches the return number
      if (barcode.toLowerCase() === returnNumber.toLowerCase()) {
        setReturnPackageVerified(true);
        setScanningStep('items');
        playAudio(true);
        setScanResult({
          success: true,
          message: 'Return package verified! Now scan individual items.',
        });
      } else {
        playAudio(false);
        setErrorMessage('Scanned barcode does not match the return number');
        setScanResult({
          success: false,
          message: 'Scanned barcode does not match the return number',
        });
      }
    },
    [returnNumber, playAudio]
  );

  const handleCheckProduct = useCallback(
    (barcode: string) => {
      setErrorMessage('');
      const index = productChecklist.findIndex(
        (item) =>
          item.barcode && item.barcode.toLowerCase() === barcode.toLowerCase()
      );

      if (index !== -1) {
        const newList = [...productChecklist];
        const currentQty = newList[index].currentQty || 0;
        if (currentQty + 1 <= newList[index].quantity) {
          newList[index].currentQty = currentQty + 1;
          setProductChecklist(newList);
          onItemScanned(newList[index].id, barcode, newList[index].currentQty);
          playAudio(true);
          setScanResult({
            success: true,
            message: `Successfully scanned: ${newList[index].title}`,
            itemId: newList[index].id,
          });
          return;
        }
        setErrorMessage('Maximum amount reached');
        playAudio(false);
        setScanResult({
          success: false,
          message: 'Maximum amount reached',
        });
        return;
      }
      playAudio(false);
      setErrorMessage('Scanned barcode does not match any product in the list');
      setScanResult({
        success: false,
        message: 'Scanned barcode does not match any product in the list',
      });
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleMarkAsScanned = useCallback(
    (itemBarcode: string) => {
      const index = productChecklist.findIndex(
        (item) => item.barcode === itemBarcode
      );
      if (index !== -1) {
        const newList = [...productChecklist];
        newList[index].currentQty = newList[index].quantity;
        setProductChecklist(newList);
        onItemScanned(newList[index].id, itemBarcode, newList[index].quantity);
      }
    },
    [productChecklist, onItemScanned]
  );

  // Exchange handlers
  const handleExchangeOutgoingScanning = useCallback(
    (scannedValue: string) => {
      setErrorMessage('');

      // Find matching item by SKU or barcode
      const matchingItem = productChecklist.find(
        (item) => item.sku === scannedValue || item.barcode === scannedValue
      );

      if (
        matchingItem &&
        (matchingItem.currentQty || 0) < matchingItem.quantity
      ) {
        // Update the scanned quantity
        setProductChecklist((prev) =>
          prev.map((item) =>
            item.id === matchingItem.id
              ? { ...item, currentQty: (item.currentQty || 0) + 1 }
              : item
          )
        );

        playAudio(true);
        setScanResult({
          success: true,
          message: `Outgoing item scanned: ${matchingItem.title}`,
          itemId: matchingItem.id,
        });

        onItemScanned(matchingItem.id, scannedValue, 1);
      } else if (
        matchingItem &&
        (matchingItem.currentQty || 0) >= matchingItem.quantity
      ) {
        playAudio(false);
        setErrorMessage(
          'This item has already been fully scanned for outgoing shipment'
        );
        setScanResult({
          success: false,
          message: 'Item already fully scanned',
        });
      } else {
        playAudio(false);
        setErrorMessage(
          'Scanned item does not match any outgoing exchange items'
        );
        setScanResult({
          success: false,
          message: 'Item not found in outgoing exchange list',
        });
      }
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleExchangeIncomingScanning = useCallback(
    (scannedValue: string) => {
      setErrorMessage('');

      // Find matching item by SKU or barcode
      const matchingItem = productChecklist.find(
        (item) => item.sku === scannedValue || item.barcode === scannedValue
      );

      if (
        matchingItem &&
        (matchingItem.currentQty || 0) < matchingItem.quantity
      ) {
        // Update the scanned quantity
        setProductChecklist((prev) =>
          prev.map((item) =>
            item.id === matchingItem.id
              ? { ...item, currentQty: (item.currentQty || 0) + 1 }
              : item
          )
        );

        playAudio(true);
        setScanResult({
          success: true,
          message: `Incoming item received: ${matchingItem.title}`,
          itemId: matchingItem.id,
        });

        onItemScanned(matchingItem.id, scannedValue, 1);
      } else if (
        matchingItem &&
        (matchingItem.currentQty || 0) >= matchingItem.quantity
      ) {
        playAudio(false);
        setErrorMessage('This item has already been fully received');
        setScanResult({
          success: false,
          message: 'Item already fully received',
        });
      } else {
        playAudio(false);
        setErrorMessage(
          'Scanned item does not match any expected incoming items'
        );
        setScanResult({
          success: false,
          message: 'Item not found in expected incoming list',
        });
      }
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleInput = useCallback(
    (value: string) => {
      clearTimeout(delayDebounce);
      if (value.trim()) {
        const delayDebounceTimeout = setTimeout(() => {
          // Route to appropriate handler based on exchange type and scanning step
          if (exchangeType === 'return' && scanningStep === 'package') {
            handleReturnPackageVerification(value.trim());
          } else if (exchangeType === 'return' && scanningStep === 'items') {
            handleCheckProduct(value.trim());
          } else if (
            exchangeType === 'exchange' &&
            exchangeStep === 'outgoing'
          ) {
            handleExchangeOutgoingScanning(value.trim());
          } else if (
            exchangeType === 'exchange' &&
            exchangeStep === 'incoming'
          ) {
            handleExchangeIncomingScanning(value.trim());
          } else {
            // Default behavior for null exchange type (legacy returns)
            handleCheckProduct(value.trim());
          }
          setBarcode('');
          // Refocus the input after processing
          if (barcodeInputRef.current) {
            barcodeInputRef.current.focus();
          }
        }, 300); // Reduced delay for faster scanning
        setDelayDebounce(delayDebounceTimeout);
      }
    },
    [
      delayDebounce,
      exchangeType,
      scanningStep,
      exchangeStep,
      handleReturnPackageVerification,
      handleCheckProduct,
      handleExchangeOutgoingScanning,
      handleExchangeIncomingScanning,
    ]
  );

  const handleBarcodeSubmit = () => {
    if (!barcode.trim()) {
      return;
    }
    clearTimeout(delayDebounce);

    // Route to appropriate handler based on exchange type and scanning step
    if (exchangeType === 'return' && scanningStep === 'package') {
      handleReturnPackageVerification(barcode.trim());
    } else if (exchangeType === 'return' && scanningStep === 'items') {
      handleCheckProduct(barcode.trim());
    } else if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      handleExchangeOutgoingScanning(barcode.trim());
    } else if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      handleExchangeIncomingScanning(barcode.trim());
    } else {
      // Default behavior for null exchange type (legacy returns)
      handleCheckProduct(barcode.trim());
    }

    setBarcode('');
    // Refocus the input after manual submission
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleBarcodeSubmit();
    }
  };

  // Calculate progress based on product checklist
  const scannedCount = productChecklist.filter(
    (item) => item.currentQty === item.quantity
  ).length;
  const totalCount = productChecklist.length;
  const allScanned = scannedCount === totalCount && totalCount > 0;

  // Helper functions for UI content
  const getCardTitle = () => {
    if (exchangeType === 'exchange') {
      return exchangeStep === 'outgoing'
        ? 'Exchange - Outgoing Items'
        : 'Exchange - Incoming Items';
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return 'Return Package Verification';
    }
    return 'Item Verification';
  };

  const getCardDescription = () => {
    if (exchangeType === 'exchange') {
      if (exchangeStep === 'outgoing') {
        return `Scan outgoing exchange items for shipment (${scannedCount}/${totalCount} scanned)`;
      }

      return `Scan incoming items received from customer (${scannedCount}/${totalCount} received)`;
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return `Scan the return package label (${returnNumber}) to verify the return`;
    }
    return `Scan barcodes to verify return items (${scannedCount}/${totalCount} verified)`;
  };

  const getProgressWidth = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return returnPackageVerified ? '50%' : '0%';
    }
    return `${(scannedCount / totalCount) * 100}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {getCardTitle()}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="ml-auto"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
          </Button>
        </CardTitle>
        <CardDescription>{getCardDescription()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Exchange step controls */}
        {exchangeType === 'exchange' && (
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button
                variant={exchangeStep === 'outgoing' ? 'default' : 'outline'}
                onClick={() => {
                  setExchangeStep('outgoing');
                  // Reset checklist when switching steps
                  setProductChecklist(
                    returnItems.map((item) => ({ ...item, currentQty: 0 }))
                  );
                  setErrorMessage('');
                  setScanResult(null);
                }}
                disabled={processed === 'exchange_shipped'}
                className="flex-1"
              >
                Outgoing Items
              </Button>
              <Button
                variant={exchangeStep === 'incoming' ? 'default' : 'outline'}
                onClick={() => {
                  setExchangeStep('incoming');
                  // Reset checklist when switching steps
                  setProductChecklist(
                    returnItems.map((item) => ({ ...item, currentQty: 0 }))
                  );
                  setErrorMessage('');
                  setScanResult(null);
                }}
                disabled={processed === 'pending'}
                className="flex-1"
              >
                Incoming Items
              </Button>
            </div>
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 text-blue-800">
              <p className="text-sm">
                {exchangeStep === 'outgoing'
                  ? 'Scan items being shipped out to the customer for exchange'
                  : 'Scan items received back from the customer'}
              </p>
            </div>
          </div>
        )}

        {/* Progress indicator */}
        <div className="h-2 w-full rounded-full bg-gray-200">
          <div
            className="h-2 rounded-full bg-blue-600 transition-all duration-300"
            style={{ width: getProgressWidth() }}
          />
        </div>

        {/* Error message */}
        {errorMessage && (
          <div className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
            <XCircle className="h-4 w-4" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}

        {/* Barcode Scanner Input */}
        {(exchangeType !== 'exchange' ||
          (exchangeType === 'exchange' &&
            (exchangeStep === 'outgoing' || exchangeStep === 'incoming'))) && (
          <div className="space-y-2">
            <Label htmlFor="barcode-input" className="font-medium text-sm">
              {exchangeType === 'return' && scanningStep === 'package'
                ? 'Return Package Scanner'
                : exchangeType === 'exchange' && exchangeStep === 'outgoing'
                  ? 'Outgoing Item Scanner'
                  : exchangeType === 'exchange' && exchangeStep === 'incoming'
                    ? 'Incoming Item Scanner'
                    : 'Barcode Scanner Input'}
            </Label>
            <div className="flex gap-2">
              <Input
                ref={barcodeInputRef}
                id="barcode-input"
                type="text"
                value={barcode}
                onChange={(e) => {
                  setBarcode(e.target.value);
                  handleInput(e.target.value);
                }}
                placeholder={
                  exchangeType === 'return' && scanningStep === 'package'
                    ? `Scan return package label (${returnNumber})...`
                    : exchangeType === 'exchange' && exchangeStep === 'outgoing'
                      ? 'Scan outgoing exchange items...'
                      : exchangeType === 'exchange' &&
                          exchangeStep === 'incoming'
                        ? 'Scan incoming items from customer...'
                        : 'Focus here and scan barcode...'
                }
                className="flex-1 rounded-md border border-gray-300 px-3 py-2 font-mono"
                onKeyDown={handleKeyDown}
                autoComplete="off"
                autoFocus
              />
              <Button
                onClick={handleBarcodeSubmit}
                disabled={!barcode.trim()}
                variant="outline"
              >
                Submit
              </Button>
            </div>
            <p className="text-gray-500 text-sm">
              {exchangeType === 'return' && scanningStep === 'package'
                ? 'Scan the return package label to verify the return before scanning individual items.'
                : exchangeType === 'exchange' && exchangeStep === 'outgoing'
                  ? 'Scan each item being shipped out for the exchange.'
                  : exchangeType === 'exchange' && exchangeStep === 'incoming'
                    ? 'Scan each item received back from the customer.'
                    : 'Keep this input focused and use your external barcode scanner to scan items.'}
            </p>
          </div>
        )}

        {/* Scan result */}
        {scanResult && (
          <div
            className={`flex items-center gap-2 rounded-lg p-3 ${
              scanResult.success
                ? 'border border-green-200 bg-green-50 text-green-800'
                : 'border border-red-200 bg-red-50 text-red-800'
            }`}
          >
            {scanResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <span className="text-sm">{scanResult.message}</span>
          </div>
        )}

        {/* Items list */}
        {((exchangeType !== 'exchange' &&
          (exchangeType !== 'return' || scanningStep === 'items')) ||
          (exchangeType === 'exchange' &&
            (exchangeStep === 'outgoing' || exchangeStep === 'incoming'))) && (
          <div className="space-y-2">
            <h4 className="font-medium">
              {exchangeType === 'exchange' && exchangeStep === 'outgoing'
                ? 'Outgoing Exchange Items'
                : exchangeType === 'exchange' && exchangeStep === 'incoming'
                  ? 'Incoming Exchange Items'
                  : 'Return Items'}
            </h4>
            {productChecklist.map((product) => {
              const scanSuccess = product.currentQty === product.quantity;

              return (
                <div
                  key={product.id}
                  className={`rounded-lg border p-3 ${
                    scanSuccess
                      ? 'border-green-200 bg-green-50'
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="font-medium">{product.title}</p>
                      {product.sku && (
                        <p className="text-gray-600 text-sm">
                          SKU: {product.sku}
                        </p>
                      )}
                      {product.barcode && (
                        <p className="text-gray-600 text-sm">
                          Barcode: {product.barcode}
                        </p>
                      )}
                      <p className="text-gray-600 text-sm">
                        Qty: {product.quantity}
                      </p>
                      <div className="mt-1 flex items-center gap-2">
                        <span
                          className={`text-sm ${
                            scanSuccess ? 'text-green-600' : 'text-red-600'
                          }`}
                        >
                          {product.currentQty} / {product.quantity} scanned
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <Badge variant={scanSuccess ? 'default' : 'secondary'}>
                        {scanSuccess ? 'Complete' : 'Pending'}
                      </Badge>
                      {scanSuccess ? (
                        <div className="flex items-center gap-1 text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          <span className="text-sm">Success</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1 text-red-600">
                            <XCircle className="h-4 w-4" />
                            <span className="text-sm">Scan to Match</span>
                          </div>
                          {product.barcode && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleMarkAsScanned(product.barcode as string)
                              }
                              disabled={scanSuccess}
                            >
                              Mark as scanned
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Submit button - show when appropriate */}
        {((exchangeType !== 'exchange' &&
          (exchangeType !== 'return' ||
            (returnPackageVerified && allScanned))) ||
          (exchangeType === 'exchange' && allScanned)) &&
          onSubmit && (
            <div className="flex justify-center pt-4">
              <Button
                onClick={() => {
                  const scannedItems = productChecklist.map((item) => ({
                    itemId: item.id,
                    scannedQuantity: item.currentQty || 0,
                  }));
                  onSubmit(scannedItems);

                  let title = 'Success';
                  let description =
                    'All items have been verified successfully!';

                  if (
                    exchangeType === 'exchange' &&
                    exchangeStep === 'outgoing'
                  ) {
                    title = 'Outgoing Items Confirmed';
                    description =
                      'All outgoing exchange items have been scanned and confirmed for shipment.';
                  } else if (
                    exchangeType === 'exchange' &&
                    exchangeStep === 'incoming'
                  ) {
                    title = 'Incoming Items Received';
                    description =
                      'All incoming exchange items have been received and confirmed.';
                  }

                  toast({
                    title,
                    description,
                  });

                  if (
                    exchangeType === 'exchange' &&
                    exchangeStep === 'outgoing'
                  ) {
                    setExchangeStep('incoming');
                    setProductChecklist(
                      returnItems.map((item) => ({ ...item, currentQty: 0 }))
                    );
                    setErrorMessage('');
                    setScanResult(null);
                  }
                }}
                className="w-full"
              >
                {exchangeType === 'exchange' && exchangeStep === 'outgoing'
                  ? 'Confirm Items & Mark as Exchange Shipped'
                  : exchangeType === 'exchange' && exchangeStep === 'incoming'
                    ? 'Confirm Received Items & Mark as Returned Received'
                    : 'Complete Verification'}
              </Button>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
