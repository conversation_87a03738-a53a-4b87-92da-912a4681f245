'use client';

import { CheckCircle } from '@repo/design-system/components/icons';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { log } from '@repo/observability/log';
import { updateReturnRequest } from '../actions';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    processed: string;
    exchangeType: string | null;
    returnNumber: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
      barcode: string | null;
    }>;
  };
}

export function BarcodeScannerTab({ returnRequest }: BarcodeScannerTabProps) {
  const handleItemScanned = (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => {
    // Here you could also save the scanned status to the database
    log.info(
      `Item ${itemId} scanned with barcode: ${scannedBarcode}, quantity: ${quantity}`
    );
  };

  const handleSubmit = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    log.info('All items verified:', scannedItems);

    // Determine the next processed status based on exchange type and current status
    let newProcessedStatus = 'completed';

    if (returnRequest.exchangeType === 'exchange') {
      if (returnRequest.processed === 'pending') {
        newProcessedStatus = 'exchange_shipped';
      } else if (returnRequest.processed === 'exchange_shipped') {
        newProcessedStatus = 'completed';
      }
    }

    log.info(`Updating return request status to: ${newProcessedStatus}`);

    await updateReturnRequest(returnRequest.id, {
      processed: newProcessedStatus,
    });
  };

  if (returnRequest.processed === 'completed') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {returnRequest.exchangeType === 'exchange'
              ? 'Exchange Processing'
              : 'Return Processing'}
          </CardTitle>
          <CardDescription className="mt-2 flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {returnRequest.exchangeType === 'exchange'
              ? 'Exchange completed successfully'
              : 'Return processed successfully'}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Show status-specific information for exchanges
  if (returnRequest.exchangeType === 'exchange') {
    const getExchangeStatusInfo = () => {
      switch (returnRequest.processed) {
        case 'pending':
          return {
            title: 'Exchange - Outgoing Items',
            description:
              'Scan items being shipped out to the customer for exchange',
          };
        case 'exchange_shipped':
          return {
            title: 'Exchange - Incoming Items',
            description: 'Scan items received back from the customer',
          };
        default:
          return {
            title: 'Exchange Processing',
            description: 'Process exchange request',
          };
      }
    };

    const statusInfo = getExchangeStatusInfo();

    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>{statusInfo.title}</CardTitle>
            <CardDescription>{statusInfo.description}</CardDescription>
          </CardHeader>
        </Card>
        <BarcodeScanner
          returnItems={returnRequest.returnItems}
          exchangeType={returnRequest.exchangeType}
          returnNumber={returnRequest.returnNumber}
          processed={returnRequest.processed}
          onItemScanned={handleItemScanned}
          onSubmit={handleSubmit}
        />
      </div>
    );
  }

  return (
    <BarcodeScanner
      returnItems={returnRequest.returnItems}
      exchangeType={returnRequest.exchangeType}
      returnNumber={returnRequest.returnNumber}
      processed={returnRequest.processed}
      onItemScanned={handleItemScanned}
      onSubmit={handleSubmit}
    />
  );
}
