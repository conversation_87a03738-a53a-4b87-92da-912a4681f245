'use client';

import { CheckCircle } from '@repo/design-system/components/icons';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { log } from '@repo/observability/log';
import { updateReturnRequest } from '../actions';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    processed: string;
    exchangeType: string | null;
    returnNumber: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
      barcode: string | null;
    }>;
  };
}

export function BarcodeScannerTab({ returnRequest }: BarcodeScannerTabProps) {
  const handleItemScanned = (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => {
    // Here you could also save the scanned status to the database
    log.info(
      `Item ${itemId} scanned with barcode: ${scannedBarcode}, quantity: ${quantity}`
    );
  };

  const handleSubmit = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    log.info('All items verified:', scannedItems);

    // set processed as true if all items are verified
    await updateReturnRequest(returnRequest.id, {
      processed: 'completed',
    });
  };

  if (returnRequest.processed === 'completed') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {returnRequest.exchangeType === 'exchange'
              ? 'Exchange Processing'
              : 'Return Processing'}
          </CardTitle>
          <CardDescription className="mt-2 flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Already processed
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <BarcodeScanner
      returnItems={returnRequest.returnItems}
      exchangeType={returnRequest.exchangeType}
      returnNumber={returnRequest.returnNumber}
      onItemScanned={handleItemScanned}
      onSubmit={handleSubmit}
    />
  );
}
