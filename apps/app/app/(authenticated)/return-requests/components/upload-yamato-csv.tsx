'use client';

import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
// shadcn/ui components - adjust paths if your setup differs
import { Button } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  type FileWithPreview,
  formatBytes,
  useFileUpload,
} from '@repo/design-system/hooks/use-file-upload';

import { toast } from '@repo/design-system/components/ui/sonner';
// Icons from lucide-react
import { AlertTriangle, UploadCloud, XCircle } from 'lucide-react';
import Papa from 'papaparse';
import { useState } from 'react';
import { processYamatoCsv } from '../actions';

export function UploadYamatoCsv() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [state, actions] = useFileUpload({
    accept: 'text/csv, .csv', // Specify accepted file types
    multiple: false, // Allow only a single file
    maxSize: 10 * 1024 * 1024, // Optional: 10MB max size limit
  });

  const { files, isDragging, errors } = state;
  const {
    getInputProps,
    openFileDialog,
    removeFile,
    clearErrors,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    clearFiles,
  } = actions;

  return (
    <div className="w-md space-y-2">
      {/* Hidden file input, controlled by the hook */}
      <input {...getInputProps()} className="hidden" />

      {/* Dropzone Area */}
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
      {/* biome-ignore lint/a11y/useFocusableInteractive: <explanation> */}
      {/* biome-ignore lint/a11y/useSemanticElements: <explanation> */}
      <div
        role="button"
        onClick={openFileDialog}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        data-dragging={isDragging || undefined}
        className="relative flex min-h-40 flex-col items-center justify-center overflow-hidden rounded-xl border border-input border-dashed p-4 transition-colors hover:bg-accent/50 has-disabled:pointer-events-none has-[input:focus]:border-ring has-[img]:border-none has-disabled:opacity-50 has-[input:focus]:ring-[3px] has-[input:focus]:ring-ring/50 data-[dragging=true]:bg-accent/50"
      >
        <UploadCloud
          className={`${isDragging ? 'text-primary' : 'text-muted-foreground'} mb-3 h-12 w-12`}
        />
        {files.length === 0 ? (
          <p className="text-center text-muted-foreground text-sm">
            {isDragging
              ? 'Drop the file here...'
              : 'Drag & drop a CSV file here, or click area to select'}
          </p>
        ) : (
          <p className="text-center text-muted-foreground text-sm">
            A file is present. Drag a new file
          </p>
        )}
      </div>

      {/* Display Validation Errors */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Validation Error</AlertTitle>
          <AlertDescription>
            <ul className="mt-1 list-disc pl-5">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
            <Button
              variant="outline"
              size="sm"
              onClick={clearErrors}
              className="mt-3"
            >
              Clear Errors
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Display Selected File Information */}
      {files.length > 0 &&
        files.map((fileWithPreview: FileWithPreview) => (
          <Card
            key={fileWithPreview.id}
            className="overflow-hidden shadow-none"
          >
            <CardContent className="py-0">
              <div className="flex items-center justify-between space-x-3">
                <div className="flex min-w-0 items-center space-x-3">
                  <div className="min-w-0">
                    <p
                      className="truncate font-medium text-sm leading-none"
                      title={fileWithPreview.file.name}
                    >
                      {fileWithPreview.file.name}
                    </p>
                    <p className="mt-1 text-muted-foreground text-xs">
                      Size: {formatBytes(fileWithPreview.file.size)}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      Type: {fileWithPreview.file.type || 'N/A'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost" // More subtle remove button, or use "destructive"
                  size="icon"
                  onClick={() => removeFile(fileWithPreview.id)}
                  aria-label="Remove file"
                >
                  <XCircle className="h-5 w-5 text-destructive hover:text-destructive/80" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

      {files.length > 0 && (
        <Button
          className="w-full"
          onClick={() => {
            setIsLoading(true);

            const fileToUpload = files[0].file;
            if (fileToUpload instanceof File) {
              Papa.parse(fileToUpload, {
                header: true,
                skipEmptyLines: true,
                delimiter: ',',
                encoding: 'UTF-8',
                transformHeader: (header) => header.trim(),
                transform: (value) => value.trim(),
                complete: async (results) => {
                  if (results.errors.length > 0) {
                    setIsLoading(false);
                    setError(
                      `Parsing errors: ${results.errors.map((e) => e.message).join(', ')}`
                    );
                    return;
                  }

                  const submission = await processYamatoCsv(
                    results.data as { [key: string]: string }[]
                  );

                  if (submission.error) {
                    console.error('Error processing CSV:', submission.error);
                    return;
                  }

                  setIsLoading(false);
                  toast.success('Tracking numbers added successfully');
                  clearFiles();
                },
                error: (error) => {
                  setIsLoading(false);
                  setError(`Parse error: ${error.message}`);
                },
              });
            }
          }}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Processing...
            </div>
          ) : (
            'Process Uploaded CSV'
          )}
        </Button>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
