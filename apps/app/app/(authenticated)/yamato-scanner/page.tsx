import type { Metada<PERSON> } from 'next';
import { Header } from '../components/header';
import { YamatoScannerClient } from './yamato-scanner-client';

const title = 'Senders Return - Yamato Scanner';
const description = 'Scan Yamato tracking numbers to find exchange requests';

export const metadata: Metadata = {
  title,
  description,
};

const YamatoScannerPage = () => {
  return (
    <>
      <Header page="Yamato Scanner" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <YamatoScannerClient />
        </div>
      </div>
    </>
  );
};

export default YamatoScannerPage;
